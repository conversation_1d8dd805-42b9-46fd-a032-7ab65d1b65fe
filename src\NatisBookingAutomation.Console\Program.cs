﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NatisBookingAutomation.Core.Interfaces;
using NatisBookingAutomation.Core.Models;
using NatisBookingAutomation.Core.Services;

namespace NatisBookingAutomation.Console;

class Program
{
    static async Task<int> Main(string[] args)
    {
        try
        {
            var host = CreateHostBuilder(args).Build();

            using var scope = host.Services.CreateScope();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
            var bookingService = scope.ServiceProvider.GetRequiredService<INatisBookingService>();

            logger.LogInformation("Starting NATIS Motorcycle License Booking Automation");
            logger.LogInformation("Application started at: {StartTime}", DateTime.Now);

            var result = await bookingService.BookMotorcycleLicenseTestAsync();

            if (result.Success)
            {
                logger.LogInformation("✅ Booking completed successfully!");
                logger.LogInformation("Centre: {Centre}", result.SelectedCentre);
                logger.LogInformation("Date: {Date}", result.BookingDate);
                logger.LogInformation("Time: {Time}", result.BookingTime);
                logger.LogInformation("Attempts used: {Attempts}", result.AttemptsUsed);

                System.Console.WriteLine();
                System.Console.WriteLine("🎉 SUCCESS! Motorcycle license test booked successfully!");
                System.Console.WriteLine($"📍 Centre: {result.SelectedCentre}");
                System.Console.WriteLine($"📅 Date: {result.BookingDate}");
                System.Console.WriteLine($"⏰ Time: {result.BookingTime}");
                System.Console.WriteLine($"🔄 Attempts: {result.AttemptsUsed}");

                return 0;
            }
            else
            {
                logger.LogError("❌ Booking failed: {Message}", result.Message);
                logger.LogInformation("Attempts used: {Attempts}", result.AttemptsUsed);

                System.Console.WriteLine();
                System.Console.WriteLine("❌ FAILED: Could not complete booking");
                System.Console.WriteLine($"Reason: {result.Message}");
                System.Console.WriteLine($"Attempts: {result.AttemptsUsed}");

                if (result.Exception != null)
                {
                    logger.LogError(result.Exception, "Exception details");
                }

                return 1;
            }
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ FATAL ERROR: {ex.Message}");
            System.Console.WriteLine("Check logs for detailed error information.");
            return 1;
        }
    }

    static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                config.AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true);
                config.AddEnvironmentVariables();
                config.AddCommandLine(args);
            })
            .ConfigureServices((context, services) =>
            {
                // Configure booking settings
                var bookingConfig = new BookingConfiguration();
                context.Configuration.GetSection("BookingConfiguration").Bind(bookingConfig);
                services.AddSingleton(bookingConfig);

                // Register services
                services.AddScoped<INatisBookingService, NatisBookingService>();

                // Configure logging
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.AddDebug();
                    builder.SetMinimumLevel(LogLevel.Information);
                });
            });
}
