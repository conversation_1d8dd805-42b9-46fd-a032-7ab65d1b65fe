﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NatisBookingAutomation.Console", "src\NatisBookingAutomation.Console\NatisBookingAutomation.Console.csproj", "{B82E18BC-B9A7-49EE-9831-305C88618EB0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NatisBookingAutomation.Core", "src\NatisBookingAutomation.Core\NatisBookingAutomation.Core.csproj", "{6BB8A01C-4C8A-460D-BB3E-9FFF6673338D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NatisBookingAutomation.Tests", "tests\NatisBookingAutomation.Tests\NatisBookingAutomation.Tests.csproj", "{0871D1EB-B999-4217-9444-9B74DCC09EFF}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{B82E18BC-B9A7-49EE-9831-305C88618EB0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B82E18BC-B9A7-49EE-9831-305C88618EB0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B82E18BC-B9A7-49EE-9831-305C88618EB0}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B82E18BC-B9A7-49EE-9831-305C88618EB0}.Debug|x64.Build.0 = Debug|Any CPU
		{B82E18BC-B9A7-49EE-9831-305C88618EB0}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B82E18BC-B9A7-49EE-9831-305C88618EB0}.Debug|x86.Build.0 = Debug|Any CPU
		{B82E18BC-B9A7-49EE-9831-305C88618EB0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B82E18BC-B9A7-49EE-9831-305C88618EB0}.Release|Any CPU.Build.0 = Release|Any CPU
		{B82E18BC-B9A7-49EE-9831-305C88618EB0}.Release|x64.ActiveCfg = Release|Any CPU
		{B82E18BC-B9A7-49EE-9831-305C88618EB0}.Release|x64.Build.0 = Release|Any CPU
		{B82E18BC-B9A7-49EE-9831-305C88618EB0}.Release|x86.ActiveCfg = Release|Any CPU
		{B82E18BC-B9A7-49EE-9831-305C88618EB0}.Release|x86.Build.0 = Release|Any CPU
		{6BB8A01C-4C8A-460D-BB3E-9FFF6673338D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6BB8A01C-4C8A-460D-BB3E-9FFF6673338D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6BB8A01C-4C8A-460D-BB3E-9FFF6673338D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6BB8A01C-4C8A-460D-BB3E-9FFF6673338D}.Debug|x64.Build.0 = Debug|Any CPU
		{6BB8A01C-4C8A-460D-BB3E-9FFF6673338D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6BB8A01C-4C8A-460D-BB3E-9FFF6673338D}.Debug|x86.Build.0 = Debug|Any CPU
		{6BB8A01C-4C8A-460D-BB3E-9FFF6673338D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6BB8A01C-4C8A-460D-BB3E-9FFF6673338D}.Release|Any CPU.Build.0 = Release|Any CPU
		{6BB8A01C-4C8A-460D-BB3E-9FFF6673338D}.Release|x64.ActiveCfg = Release|Any CPU
		{6BB8A01C-4C8A-460D-BB3E-9FFF6673338D}.Release|x64.Build.0 = Release|Any CPU
		{6BB8A01C-4C8A-460D-BB3E-9FFF6673338D}.Release|x86.ActiveCfg = Release|Any CPU
		{6BB8A01C-4C8A-460D-BB3E-9FFF6673338D}.Release|x86.Build.0 = Release|Any CPU
		{0871D1EB-B999-4217-9444-9B74DCC09EFF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0871D1EB-B999-4217-9444-9B74DCC09EFF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0871D1EB-B999-4217-9444-9B74DCC09EFF}.Debug|x64.ActiveCfg = Debug|Any CPU
		{0871D1EB-B999-4217-9444-9B74DCC09EFF}.Debug|x64.Build.0 = Debug|Any CPU
		{0871D1EB-B999-4217-9444-9B74DCC09EFF}.Debug|x86.ActiveCfg = Debug|Any CPU
		{0871D1EB-B999-4217-9444-9B74DCC09EFF}.Debug|x86.Build.0 = Debug|Any CPU
		{0871D1EB-B999-4217-9444-9B74DCC09EFF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0871D1EB-B999-4217-9444-9B74DCC09EFF}.Release|Any CPU.Build.0 = Release|Any CPU
		{0871D1EB-B999-4217-9444-9B74DCC09EFF}.Release|x64.ActiveCfg = Release|Any CPU
		{0871D1EB-B999-4217-9444-9B74DCC09EFF}.Release|x64.Build.0 = Release|Any CPU
		{0871D1EB-B999-4217-9444-9B74DCC09EFF}.Release|x86.ActiveCfg = Release|Any CPU
		{0871D1EB-B999-4217-9444-9B74DCC09EFF}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{B82E18BC-B9A7-49EE-9831-305C88618EB0} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{6BB8A01C-4C8A-460D-BB3E-9FFF6673338D} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{0871D1EB-B999-4217-9444-9B74DCC09EFF} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
	EndGlobalSection
EndGlobal
