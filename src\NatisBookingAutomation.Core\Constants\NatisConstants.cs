namespace NatisBookingAutomation.Core.Constants;

public static class NatisConstants
{
    public const string BaseUrl = "https://online.natis.gov.za/#/";
    public const string LoginUrl = "https://online.natis.gov.za/#/auth/login";
    public const string IdentifyUrl = "https://online.natis.gov.za/#/auth/identify";

    // Selectors
    public static class Selectors
    {
        public const string LoginLink = "text=Login";
        public const string RegisterLink = "text=Register";
        public const string ProfileName = "[data-testid='profile-name']";
        public const string UsernameInput = "[data-testid='username']";
        public const string PasswordInput = "[data-testid='password']";
        public const string LoginButton = "[data-testid='login-button']";
        public const string BookNowButton = "text=Book now for Driving Licence Test";
        public const string ProvinceDropdown = "[data-testid='province-dropdown']";
        public const string ContinueButton = "text=Continue";
        public const string TestCategoryDropdown = "[data-testid='test-category-dropdown']";
        public const string LicenceTestTypeDropdown = "[data-testid='licence-test-type-dropdown']";
        public const string CentreDropdown = "[data-testid='centre-dropdown']";
        public const string DatePicker = "[data-testid='date-picker']";
        public const string TimeSlotDropdown = "[data-testid='time-slot-dropdown']";
        public const string BookOnlyButton = "text=BOOK ONLY";
        public const string CancelButton = "text=Cancel";
    }

    // Wait times in milliseconds
    public static class WaitTimes
    {
        public const int UltraShort = 200;
        public const int Short = 2000;
        public const int Medium = 5000;
        public const int Long = 10000;
        public const int ExtraLong = 30000;
    }
}
