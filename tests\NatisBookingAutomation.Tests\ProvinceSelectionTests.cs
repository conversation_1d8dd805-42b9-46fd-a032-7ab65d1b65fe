using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using NatisBookingAutomation.Core.Models;
using NatisBookingAutomation.Core.Services;
using NUnit.Framework;

namespace NatisBookingAutomation.Tests;

[TestFixture]
[Category("Unit")]
public class ProvinceSelectionTests
{
    private ILogger<NatisBookingService> logger = null!;
    private BookingConfiguration config = null!;

    [SetUp]
    public void Setup()
    {
        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        logger = loggerFactory.CreateLogger<NatisBookingService>();

        config = new BookingConfiguration
        {
            Username = "<EMAIL>",
            Password = "test-password",
            Province = "Gauteng",
            MaxRetryAttempts = 1,
            RetryDelaySeconds = 1,
            PageTimeoutSeconds = 10,
            HeadlessMode = true
        };
    }

    [Test]
    public void BookingConfiguration_Province_ShouldBeGauteng()
    {
        // Arrange & Act
        var testConfig = new BookingConfiguration();

        // Assert
        Assert.That(testConfig.Province, Is.EqualTo("Gauteng"));
    }

    [Test]
    public void BookingConfiguration_Province_ShouldBeConfigurable()
    {
        // Arrange
        var testConfig = new BookingConfiguration
        {
            Province = "Western Cape"
        };

        // Act & Assert
        Assert.That(testConfig.Province, Is.EqualTo("Western Cape"));
    }

    [Test]
    public void NatisBookingService_Constructor_ShouldAcceptConfiguration()
    {
        // Arrange & Act
        using var service = new NatisBookingService(logger, config);

        // Assert
        Assert.That(service, Is.Not.Null);
    }

    [Test]
    public void BookingConfiguration_DefaultProvince_ShouldMatchExpectedValue()
    {
        // Arrange
        var defaultConfig = new BookingConfiguration();

        // Act & Assert
        Assert.That(defaultConfig.Province, Is.EqualTo("Gauteng"));
        Assert.That(defaultConfig.Province, Is.Not.Empty);
        Assert.That(defaultConfig.Province, Does.Not.Contain("gauteng")); // Should be proper case
    }
}
