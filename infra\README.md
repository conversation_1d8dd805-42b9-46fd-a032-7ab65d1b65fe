# Infrastructure Deployment

## Overview

This directory contains infrastructure-as-code templates for deploying the NATIS Booking Automation solution to various cloud platforms.

## Deployment Options

### Local Development

For local development and testing:

1. **Prerequisites**:
   - .NET 10.0 SDK
   - Microsoft Edge or Chromium browser
   - Git

2. **Setup**:
   ```bash
   git clone <repository-url>
   cd playwright-c#
   dotnet restore
   dotnet build
   playwright install
   ```

3. **Configuration**:
   - Copy `appsettings.json` to `appsettings.Local.json`
   - Update credentials and preferences
   - Set `HeadlessMode: false` for debugging

4. **Run**:
   ```bash
   dotnet run --project src/NatisBookingAutomation.Console --environment Local
   ```

### Azure Container Instances (Recommended)

For scheduled automation runs:

1. **Create Azure Resources**:
   ```bash
   # Create resource group
   az group create --name natis-booking-rg --location southafricanorth
   
   # Create container registry
   az acr create --resource-group natis-booking-rg --name natisbookingacr --sku Basic
   ```

2. **Build and Push Container**:
   ```bash
   # Build Docker image
   docker build -t natis-booking-automation .
   
   # Tag and push to ACR
   az acr login --name natisbookingacr
   docker tag natis-booking-automation natisbookingacr.azurecr.io/natis-booking:latest
   docker push natisbookingacr.azurecr.io/natis-booking:latest
   ```

3. **Deploy Container Instance**:
   ```bash
   az container create \
     --resource-group natis-booking-rg \
     --name natis-booking-instance \
     --image natisbookingacr.azurecr.io/natis-booking:latest \
     --cpu 1 --memory 2 \
     --restart-policy Never \
     --environment-variables ASPNETCORE_ENVIRONMENT=Production
   ```

### GitHub Actions (CI/CD)

For automated deployments:

1. **Setup Secrets**:
   - `AZURE_CREDENTIALS`: Service principal credentials
   - `NATIS_USERNAME`: NATIS login username
   - `NATIS_PASSWORD`: NATIS login password

2. **Workflow Configuration**:
   ```yaml
   name: Deploy NATIS Booking Automation
   
   on:
     schedule:
       - cron: '0 6 * * *'  # Run daily at 6 AM
     workflow_dispatch:
   
   jobs:
     deploy:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v3
         - name: Setup .NET
           uses: actions/setup-dotnet@v3
           with:
             dotnet-version: '10.0.x'
         - name: Build and Deploy
           run: |
             dotnet build
             # Deploy to Azure Container Instances
   ```

### Docker Deployment

For containerized deployments:

1. **Create Dockerfile**:
   ```dockerfile
   FROM mcr.microsoft.com/dotnet/runtime:10.0
   WORKDIR /app
   COPY bin/Release/net10.0/publish/ .
   RUN playwright install --with-deps chromium
   ENTRYPOINT ["dotnet", "NatisBookingAutomation.Console.dll"]
   ```

2. **Build and Run**:
   ```bash
   docker build -t natis-booking .
   docker run -e ASPNETCORE_ENVIRONMENT=Production natis-booking
   ```

## Configuration Management

### Environment Variables

Override configuration using environment variables:

```bash
export BookingConfiguration__Username="<EMAIL>"
export BookingConfiguration__Password="your-password"
export BookingConfiguration__HeadlessMode="true"
export BookingConfiguration__MaxRetryAttempts="5"
```

### Azure Key Vault Integration

For production deployments, store sensitive configuration in Azure Key Vault:

```bash
# Create Key Vault
az keyvault create --name natis-booking-kv --resource-group natis-booking-rg

# Store secrets
az keyvault secret set --vault-name natis-booking-kv --name "natis-username" --value "<EMAIL>"
az keyvault secret set --vault-name natis-booking-kv --name "natis-password" --value "your-password"
```

## Monitoring and Logging

### Application Insights

For production monitoring:

```bash
# Create Application Insights
az monitor app-insights component create \
  --app natis-booking-insights \
  --location southafricanorth \
  --resource-group natis-booking-rg
```

### Log Analytics

For centralized logging:

```bash
# Create Log Analytics workspace
az monitor log-analytics workspace create \
  --resource-group natis-booking-rg \
  --workspace-name natis-booking-logs
```

## Scheduling

### Azure Logic Apps

For scheduled execution:

1. Create Logic App with HTTP trigger
2. Configure recurrence (daily at 6 AM)
3. Add HTTP action to trigger Container Instance
4. Add email notification on success/failure

### Cron Jobs (Linux)

For Linux-based scheduling:

```bash
# Add to crontab
0 6 * * * /usr/bin/docker run --rm natis-booking-automation
```

## Security Considerations

1. **Credentials**: Never store credentials in source code
2. **Network**: Use private networks for container communication
3. **Access**: Implement least-privilege access policies
4. **Monitoring**: Enable security monitoring and alerting
5. **Updates**: Regularly update base images and dependencies

## Troubleshooting

### Common Issues

1. **Browser Installation**: Ensure Playwright browsers are installed in container
2. **Memory Limits**: Increase memory allocation for browser operations
3. **Network Timeouts**: Configure appropriate timeout values
4. **Headless Mode**: Enable headless mode for server deployments

### Debugging

1. **Local Testing**: Run locally with `HeadlessMode: false`
2. **Container Logs**: Check container logs for error details
3. **Screenshots**: Enable screenshot capture on failures
4. **Verbose Logging**: Set log level to Debug for detailed information

## Cost Optimization

1. **Container Instances**: Use consumption-based pricing
2. **Scheduling**: Run only when needed (daily/weekly)
3. **Resource Sizing**: Right-size CPU and memory allocation
4. **Storage**: Use minimal storage for logs and screenshots

## Support

For deployment issues:
1. Check application logs
2. Verify configuration settings
3. Test network connectivity
4. Review Azure resource status
