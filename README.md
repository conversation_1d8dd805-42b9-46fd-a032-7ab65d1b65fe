# NATIS Motorcycle License Booking Automation

## Overview

This is a comprehensive Playwright C# automation solution for booking motorcycle driver's license tests on the South African NATIS online system. The application handles the complete booking flow with robust error handling, retry logic, and comprehensive logging.

## Features

- **Multi-browser Support**: Targets Microsoft Edge (primary) with Chromium fallback
- **Robust Authentication**: Handles login flow with session management
- **Smart Centre Selection**: Automatically selects from preferred centres with available slots
- **Retry Logic**: Configurable retry attempts with intelligent backoff
- **Comprehensive Logging**: Detailed logging for debugging and monitoring
- **Screenshot Capture**: Automatic screenshots on failures for debugging
- **Configuration Management**: JSON-based configuration with environment overrides

## Technical Stack

- **.NET 10.0** (Preview)
- **Microsoft Playwright** for browser automation
- **Microsoft.Extensions.Hosting** for dependency injection and configuration
- **Microsoft.Extensions.Logging** for structured logging
- **NUnit** for testing

## Project Structure

```
├── src/
│   ├── NatisBookingAutomation.Core/     # Core automation logic
│   │   ├── Constants/                   # Application constants
│   │   ├── Interfaces/                  # Service interfaces
│   │   ├── Models/                      # Data models
│   │   └── Services/                    # Business logic services
│   └── NatisBookingAutomation.Console/  # Console application
├── tests/
│   └── NatisBookingAutomation.Tests/    # Unit and integration tests
└── docs/                                # Documentation
```

## Prerequisites

- .NET 10.0 SDK (Preview)
- Microsoft Edge or Chromium browser
- Visual Studio 2022 or VS Code

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd playwright-c#
   ```

2. **Restore dependencies**:
   ```bash
   dotnet restore
   ```

3. **Install Playwright browsers**:
   ```bash
   dotnet build
   playwright install
   ```

## Configuration

### Basic Configuration

Edit `src/NatisBookingAutomation.Console/appsettings.json`:

```json
{
  "BookingConfiguration": {
    "Username": "<EMAIL>",
    "Password": "your-password",
    "ProfileName": "YOUR FULL NAME",
    "Province": "Gauteng",
    "TestCategory": "03 - DRIVING LICENSE FOR MOTOR CYCLE",
    "LicenceTestType": "A - MOTOR CYCLE, EXCEEDING 125 CM3",
    "PreferredCentres": [
      "Roodepoort DLTC",
      "Krugersdorp DLTC",
      "Randfontein DLTC",
      "Westonaria DLTC",
      "Langlaagte DLTC",
      "Randburg DLTC",
      "Sandton DLTC"
    ],
    "MaxRetryAttempts": 10,
    "RetryDelaySeconds": 30,
    "PageTimeoutSeconds": 30,
    "HeadlessMode": false
  }
}
```

### Environment-Specific Configuration

For development, edit `appsettings.Development.json` to override settings:

```json
{
  "BookingConfiguration": {
    "HeadlessMode": false,
    "MaxRetryAttempts": 3,
    "RetryDelaySeconds": 10
  }
}
```

## Usage

### Running the Application

```bash
# Run in development mode
dotnet run --project src/NatisBookingAutomation.Console

# Run in production mode
dotnet run --project src/NatisBookingAutomation.Console --environment Production

# Run with custom configuration
dotnet run --project src/NatisBookingAutomation.Console -- --BookingConfiguration:HeadlessMode=true
```

### Command Line Arguments

The application supports configuration via command line:

```bash
dotnet run --project src/NatisBookingAutomation.Console -- \
  --BookingConfiguration:MaxRetryAttempts=5 \
  --BookingConfiguration:HeadlessMode=true \
  --BookingConfiguration:RetryDelaySeconds=60
```

## Automation Flow

1. **Navigate to NATIS Portal**: Opens https://online.natis.gov.za/#/
2. **Authentication**: Checks login status and performs login if needed
3. **Booking Navigation**: Clicks "Book now for Driving Licence Test"
4. **Province Selection**: Selects "Gauteng" and continues
5. **Test Configuration**: Sets motorcycle test category and licence type
6. **Centre Selection**: Finds preferred centres with available slots
7. **Date/Time Selection**: Selects earliest available slot
8. **Booking Completion**: Completes the booking process
9. **Success Celebration**: Navigates to Rick Astley video on success

## Error Handling

- **Retry Logic**: Automatically retries failed attempts up to configured limit
- **Screenshot Capture**: Takes screenshots on failures for debugging
- **Comprehensive Logging**: Logs all actions and errors with timestamps
- **Graceful Degradation**: Falls back to alternative selectors if primary ones fail

## Logging

Logs are written to console with structured format:
- **Information**: General flow and progress updates
- **Warning**: Non-critical issues and fallbacks
- **Error**: Failures and exceptions
- **Debug**: Detailed debugging information (Development mode)

## Testing

```bash
# Run all tests
dotnet test

# Run tests with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run specific test category
dotnet test --filter Category=Integration
```

## Troubleshooting

### Common Issues

1. **Browser Not Found**: Ensure Playwright browsers are installed with `playwright install`
2. **Login Failures**: Verify credentials in configuration
3. **Element Not Found**: Check if NATIS website structure has changed
4. **Timeout Issues**: Increase `PageTimeoutSeconds` in configuration

### Debug Mode

Run with debug logging:
```bash
dotnet run --project src/NatisBookingAutomation.Console --environment Development
```

### Screenshots

Failed attempts automatically capture screenshots in the `screenshots/` directory.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is for educational and personal use only. Please ensure compliance with NATIS terms of service.

## Disclaimer

This automation tool is provided as-is. Users are responsible for ensuring compliance with NATIS terms of service and applicable laws. The authors are not responsible for any misuse or consequences of using this tool.
