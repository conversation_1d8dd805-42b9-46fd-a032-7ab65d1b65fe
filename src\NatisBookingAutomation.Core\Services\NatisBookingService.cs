using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using NatisBookingAutomation.Core.Constants;
using NatisBookingAutomation.Core.Interfaces;
using NatisBookingAutomation.Core.Models;
using System.Text.RegularExpressions;

namespace NatisBookingAutomation.Core.Services;

public class NatisBookingService : INatisBookingService, IDisposable
{
    private readonly ILogger<NatisBookingService> logger;
    private readonly BookingConfiguration config;
    private IPlaywright? playwright;
    private IBrowser? browser;
    private IPage? page;
    private bool disposed = false;

    public NatisBookingService(ILogger<NatisBookingService> logger, BookingConfiguration config)
    {
        this.logger = logger;
        this.config = config;
    }

    public async Task<BookingResult> BookMotorcycleLicenseTestAsync(CancellationToken cancellationToken = default)
    {
        var result = new BookingResult();
        var attempt = 0;

        try
        {
            await InitializeBrowserAsync();

            while (attempt < config.MaxRetryAttempts && !result.Success)
            {
                attempt++;
                logger.LogInformation("Starting booking attempt {Attempt} of {MaxAttempts}", attempt, config.MaxRetryAttempts);
                result.Logs.Add($"Attempt {attempt}: Starting booking process");

                try
                {
                    // Navigate to NATIS portal
                    await NavigateToPortalAsync();
                    result.Logs.Add("Navigated to NATIS portal");

                    // Handle authentication
                    if (!await IsLoggedInAsync())
                    {
                        logger.LogInformation("User not logged in, attempting login");
                        if (!await LoginAsync())
                        {
                            result.Message = "Failed to login";
                            result.Logs.Add("Login failed");
                            continue;
                        }
                        result.Logs.Add("Successfully logged in");
                    }
                    else
                    {
                        result.Logs.Add("Already logged in");
                    }

                    // Navigate to booking section
                    await NavigateToBookingSectionAsync();
                    result.Logs.Add("Navigated to booking section");

                    // Select province
                    await SelectProvinceAsync();
                    result.Logs.Add($"Selected province: {config.Province}");

                    // Configure test category
                    await ConfigureTestCategoryAsync();
                    result.Logs.Add("Configured test category and licence type");

                    // Get available centres
                    var availableCentres = await GetAvailableCentersAsync();
                    result.Logs.Add($"Found {availableCentres.Count} centres with availability");

                    // Find preferred centre with slots
                    var selectedCentre = availableCentres
                        .Where(c => c.IsPreferred && c.AvailableSlots > 0)
                        .OrderByDescending(c => c.AvailableSlots)
                        .FirstOrDefault();

                    break;

                    if (selectedCentre == null)
                    {
                        logger.LogWarning("No preferred centres have available slots. Retrying in {Delay} seconds", config.RetryDelaySeconds);
                        result.Logs.Add("No available slots at preferred centres");

                        // Click cancel to go back to dashboard
                        await ClickCancelAsync();

                        if (attempt < config.MaxRetryAttempts)
                        {
                            await Task.Delay(TimeSpan.FromSeconds(config.RetryDelaySeconds), cancellationToken);
                        }
                        continue;
                    }

                    // Select centre and book
                    await SelectCentreAsync(selectedCentre.OptionIndex, selectedCentre.Name);
                    result.Logs.Add($"Selected centre: {selectedCentre.Name}");

                    await SelectEarliestDateAndTimeAsync();
                    result.Logs.Add("Selected earliest available date and time");

                    await CompleteBookingAsync();
                    result.Logs.Add("Completed booking");

                    // Success!
                    result.Success = true;
                    result.SelectedCentre = selectedCentre.Name;
                    result.Message = "Booking completed successfully";
                    result.AttemptsUsed = attempt;

                    // Navigate to success video
                    await NavigateToSuccessVideoAsync();
                    result.Logs.Add("Navigated to success video");

                    logger.LogInformation("Booking completed successfully at {Centre} after {Attempts} attempts",
                        selectedCentre.Name, attempt);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error during booking attempt {Attempt}", attempt);
                    result.Logs.Add($"Attempt {attempt} failed: {ex.Message}");

                    // Take screenshot on error
                    await TakeScreenshotAsync($"error_attempt_{attempt}");

                    if (attempt < config.MaxRetryAttempts)
                    {
                        await Task.Delay(TimeSpan.FromSeconds(config.RetryDelaySeconds), cancellationToken);
                    }
                }
            }

            if (!result.Success)
            {
                result.Message = $"Failed to complete booking after {config.MaxRetryAttempts} attempts";
                result.AttemptsUsed = attempt;
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Fatal error during booking process");
            result.Exception = ex;
            result.Message = $"Fatal error: {ex.Message}";
            result.AttemptsUsed = attempt;
        }

        return result;
    }

    private async Task InitializeBrowserAsync()
    {
        if (playwright == null)
        {
            playwright = await Playwright.CreateAsync();

            var browserOptions = new BrowserTypeLaunchOptions
            {
                Headless = config.HeadlessMode,
                Timeout = config.PageTimeoutSeconds * 1000
            };

            // Try Edge first, fallback to Chromium
            //try
            //{
            //    var edgeOptions = new BrowserTypeLaunchOptions
            //    {
            //        Headless = config.HeadlessMode,
            //        Timeout = config.PageTimeoutSeconds * 1000,
            //        Channel = "msedge"
            //    };
            //    browser = await playwright.Chromium.LaunchAsync(edgeOptions);
            //    logger.LogInformation("Launched Microsoft Edge browser");
            //}
            //catch
            //{
            //    browser = await playwright.Chromium.LaunchAsync(browserOptions);
            //    logger.LogInformation("Launched Chromium browser (Edge not available)");
            //}

            browser = await playwright.Chromium.LaunchAsync(browserOptions);
            logger.LogInformation("Launched Chromium browser");

            page = await browser.NewPageAsync();
            page.SetDefaultTimeout(config.PageTimeoutSeconds * 1000);
        }
    }

    private async Task NavigateToPortalAsync()
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        logger.LogInformation("Navigating to NATIS portal: {Url}", NatisConstants.BaseUrl);
        await page.GotoAsync(NatisConstants.BaseUrl, new PageGotoOptions { WaitUntil = WaitUntilState.NetworkIdle });

        // Wait for the page to fully load
        // Normally this takes about 3 seconds to load all the resources like JavaScript and CSS, so we give it 5 seconds (Medium) to load everything.  Thereafter navigation is quite fast.
        await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
        await Task.Delay(NatisConstants.WaitTimes.Medium);

        logger.LogInformation("Successfully navigated to portal. Current URL: {Url}", page.Url);
    }

    public async Task<bool> IsLoggedInAsync(CancellationToken cancellationToken = default)
    {
        if (page == null) return false;

        try
        {
            // Check if profile name is visible
            var profileElement = await page.QuerySelectorAsync($"text={config.ProfileName}");
            if (profileElement != null)
            {
                logger.LogInformation("User is already logged in as {ProfileName}", config.ProfileName);
                return true;
            }

            // Check if login/register links are visible
            var loginLink = await page.QuerySelectorAsync(NatisConstants.Selectors.LoginLink);
            var registerLink = await page.QuerySelectorAsync(NatisConstants.Selectors.RegisterLink);

            return loginLink == null && registerLink == null;
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Error checking login status");
            return false;
        }
    }

    public async Task<bool> LoginAsync(CancellationToken cancellationToken = default)
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        try
        {
            logger.LogInformation("Attempting to login with username: {Username}", config.Username);

            // Take screenshot for debugging
            await TakeScreenshotAsync("before_login_attempt");

            // Debug: Log current page URL and title
            logger.LogInformation("Current page URL: {Url}", page.Url);
            logger.LogInformation("Current page title: {Title}", await page.TitleAsync());

            // Debug: Check what elements are actually on the page
            await DebugPageElementsAsync();

            // Try multiple selectors for the login link
            var loginSelectors = new[]
            {
                NatisConstants.Selectors.LoginLink, // "text=Login"
                "a:has-text('Login')",
                "button:has-text('Login')",
                "[href*='login']",
                "[data-testid*='login']",
                "a[href='#/auth/login']",
                "a[href*='auth/login']",
                ".login",
                "#login",
                "text=Log in",
                "text=LOG IN",
                "text=Sign In",
                "text=SIGN IN"
            };

            //IElementHandle? loginLink = null;
            //string? usedSelector = null;

            //foreach (var selector in loginSelectors)
            //{
            //    try
            //    {
            //        loginLink = await page.QuerySelectorAsync(selector);
            //        if (loginLink != null)
            //        {
            //            // Check if element is visible
            //            var isVisible = await loginLink.IsVisibleAsync();
            //            logger.LogInformation("Found login element with selector '{Selector}', visible: {IsVisible}", selector, isVisible);

            //            if (isVisible)
            //            {
            //                usedSelector = selector;
            //                break;
            //            }
            //            else
            //            {
            //                logger.LogWarning("Login element found but not visible with selector: {Selector}", selector);
            //            }
            //        }
            //    }
            //    catch (Exception ex)
            //    {
            //        logger.LogDebug(ex, "Failed to find login element with selector: {Selector}", selector);
            //    }
            //}

            //if (loginLink != null && usedSelector != null)
            //{
            //    logger.LogInformation("Clicking login link using selector: {Selector}", usedSelector);

            //    // Try multiple click approaches
            //    try
            //    {
            //        // First try: Regular click
            //        await loginLink.ClickAsync();
            //        await Task.Delay(2000); // Wait for potential navigation

            //        // Check if URL changed
            //        var currentUrl = page.Url;
            //        logger.LogInformation("After regular click - URL: {Url}", currentUrl);

            //        if (!currentUrl.Contains("login") && !currentUrl.Contains("auth"))
            //        {
            //            logger.LogWarning("Regular click didn't navigate. Trying JavaScript click.");

            //            // Second try: JavaScript click
            //            await page.EvaluateAsync($"document.querySelector('{usedSelector}').click()");
            //            await Task.Delay(2000);

            //            currentUrl = page.Url;
            //            logger.LogInformation("After JavaScript click - URL: {Url}", currentUrl);

            //            if (!currentUrl.Contains("login") && !currentUrl.Contains("auth"))
            //            {
            //                logger.LogWarning("JavaScript click didn't navigate either. Trying force click.");

            //                // Third try: Force click
            //                await loginLink.ClickAsync(new ElementHandleClickOptions { Force = true });
            //                await Task.Delay(2000);

            //                currentUrl = page.Url;
            //                logger.LogInformation("After force click - URL: {Url}", currentUrl);
            //            }
            //        }

            //        await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            //        await TakeScreenshotAsync("after_login_click");
            //    }
            //    catch (Exception ex)
            //    {
            //        logger.LogWarning(ex, "Error during login link click attempts");
            //    }
            //}

            // Always try direct navigation as fallback
            //if (!page.Url.Contains("login") && !page.Url.Contains("auth"))
            //{
            //    logger.LogInformation("Login link click didn't work. Attempting direct navigation to login page");
            //    await page.GotoAsync(NatisConstants.LoginUrl);
            //    await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            //    await Task.Delay(NatisConstants.WaitTimes.UltraShort);
            //    await TakeScreenshotAsync("direct_login_navigation");
            //}
            await page.GotoAsync(NatisConstants.LoginUrl);
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

            // Wait for page to load after login click and debug current state
            //await Task.Delay(NatisConstants.WaitTimes.Medium);
            //logger.LogInformation("After login click - Current URL: {Url}", page.Url);
            //await TakeScreenshotAsync("after_login_click_page_loaded");

            // Handle redirect from identify page to login page
            if (page.Url.Contains("/auth/identify"))
            {
                logger.LogInformation("Redirecting from identify page to login page");
                await page.GotoAsync(NatisConstants.LoginUrl);
                await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
                await Task.Delay(NatisConstants.WaitTimes.Medium);
                await TakeScreenshotAsync("after_redirect_to_login");
            }

            // Debug current page state before trying to fill form
            logger.LogInformation("About to fill login form. Current URL: {Url}", page.Url);
            await DebugPageElementsAsync();

            // Try multiple selectors for username field
            var usernameSelectors = new[]
            {
                "input[type='email']",
                "input[name='username']",
                "input[id='username']",
                "input[name='email']",
                "input[id='email']",
                "[data-testid='username']",
                "[data-testid='email']",
                "input[placeholder*='email']",
                "input[placeholder*='username']",
                "#username",
                "#email"
            };

            IElementHandle? usernameField = null;
            string? usernameSelector = null;

            foreach (var selector in usernameSelectors)
            {
                try
                {
                    usernameField = await page.QuerySelectorAsync(selector);
                    if (usernameField != null)
                    {
                        var isVisible = await usernameField.IsVisibleAsync();
                        logger.LogInformation("Found username field with selector '{Selector}', visible: {IsVisible}", selector, isVisible);

                        if (isVisible)
                        {
                            usernameSelector = selector;
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    logger.LogDebug(ex, "Failed to find username field with selector: {Selector}", selector);
                }
            }

            if (usernameField == null || usernameSelector == null)
            {
                logger.LogError("Could not find username input field. Taking screenshot for debugging.");
                await TakeScreenshotAsync("no_username_field_found");
                return false;
            }

            // Fill username
            logger.LogInformation("Filling username field using selector: {Selector}", usernameSelector);
            await usernameField.FillAsync(config.Username);

            // Try multiple selectors for password field
            var passwordSelectors = new[]
            {
                "input[type='password']",
                "input[name='password']",
                "input[id='password']",
                "[data-testid='password']",
                "input[placeholder*='password']",
                "#password"
            };

            IElementHandle? passwordField = null;
            string? passwordSelector = null;

            foreach (var selector in passwordSelectors)
            {
                try
                {
                    passwordField = await page.QuerySelectorAsync(selector);
                    if (passwordField != null)
                    {
                        var isVisible = await passwordField.IsVisibleAsync();
                        logger.LogInformation("Found password field with selector '{Selector}', visible: {IsVisible}", selector, isVisible);

                        if (isVisible)
                        {
                            passwordSelector = selector;
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    logger.LogDebug(ex, "Failed to find password field with selector: {Selector}", selector);
                }
            }

            if (passwordField == null || passwordSelector == null)
            {
                logger.LogError("Could not find password input field. Taking screenshot for debugging.");
                await TakeScreenshotAsync("no_password_field_found");
                return false;
            }

            // Fill password
            logger.LogInformation("Filling password field using selector: {Selector}", passwordSelector);
            await passwordField.FillAsync(config.Password);

            await TakeScreenshotAsync("form_filled");

            // Try multiple selectors for login button
            var loginButtonSelectors = new[]
            {
                "button[type='submit']",
                "input[type='submit']",
                "button:has-text('Login')",
                "button:has-text('Sign In')",
                "button:has-text('LOG IN')",
                "button:has-text('SIGN IN')",
                "[data-testid='login-button']",
                "[data-testid='submit']",
                ".login-button",
                "#login-button"
            };

            IElementHandle? loginButton = null;
            string? loginButtonSelector = null;

            foreach (var selector in loginButtonSelectors)
            {
                try
                {
                    loginButton = await page.QuerySelectorAsync(selector);
                    if (loginButton != null)
                    {
                        var isVisible = await loginButton.IsVisibleAsync();
                        logger.LogInformation("Found login button with selector '{Selector}', visible: {IsVisible}", selector, isVisible);

                        if (isVisible)
                        {
                            loginButtonSelector = selector;
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    logger.LogDebug(ex, "Failed to find login button with selector: {Selector}", selector);
                }
            }

            if (loginButton == null || loginButtonSelector == null)
            {
                logger.LogError("Could not find login button. Taking screenshot for debugging.");
                await TakeScreenshotAsync("no_login_button_found");
                return false;
            }

            // Click login button
            logger.LogInformation("Clicking login button using selector: {Selector}", loginButtonSelector);
            await Task.Delay(NatisConstants.WaitTimes.UltraShort);
            await loginButton.ClickAsync();

            // Wait for navigation or login completion
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            await Task.Delay(NatisConstants.WaitTimes.UltraShort);   // was Medium
            await TakeScreenshotAsync("after_login_submit");

            // Verify login success
            return await IsLoggedInAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Login failed");
            return false;
        }
    }

    private async Task NavigateToBookingSectionAsync()
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        logger.LogInformation("Navigating to booking section");
        await page.ClickAsync(NatisConstants.Selectors.BookNowButton);
        await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
        await Task.Delay(NatisConstants.WaitTimes.UltraShort);
    }

    private async Task SelectProvinceAsync()
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        logger.LogInformation("Selecting province: {Province}", config.Province);

        // Take screenshot before attempting province selection
        await TakeScreenshotAsync("before_province_selection");

        // 4 = Gauteng
        await page.Locator("#provinceCd").SelectOptionAsync(new[] { "4: Object" });
        // 6 - Eastern Cape for testing because there's usually open slots.
        await page.Locator("#provinceCd").SelectOptionAsync(new[] { "6: Object" });

        // Click continue button
        await page.ClickAsync(NatisConstants.Selectors.ContinueButton);
        await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
        await Task.Delay(NatisConstants.WaitTimes.UltraShort);
    }

    private async Task ConfigureTestCategoryAsync()
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        logger.LogInformation("Configuring test category: {TestCategory}", config.TestCategory);

        // Car
        await page.Locator("#dlExamrTestCat").SelectOptionAsync(new[] { "1: Object" });
        await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
        await Task.Delay(NatisConstants.WaitTimes.UltraShort);
        await Task.Delay(NatisConstants.WaitTimes.UltraShort);

        // Motorcycle
        await page.Locator("#dlExamrTestCat").SelectOptionAsync(new[] { "0: Object" });
        await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
        await Task.Delay(NatisConstants.WaitTimes.UltraShort);
        await Task.Delay(NatisConstants.WaitTimes.UltraShort);

        // Code A
        await page.Locator("select[name=\"dlTstLicType\"]").SelectOptionAsync(new[] { "2: Object" });
        await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
        await Task.Delay(NatisConstants.WaitTimes.UltraShort);
    }

    public async Task<List<CentreAvailability>> GetAvailableCentersAsync(CancellationToken cancellationToken = default)
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        var centers = new List<CentreAvailability>();

        try
        {
            logger.LogInformation("Getting available centres");

            var testingCentersDropDown = this.page.Locator("select[name=\"infN\"]");

            List<string> options = [];

            await testingCentersDropDown.AllInnerTextsAsync().ContinueWith(t =>
            {
                foreach (var text in t.Result)
                {
                    options = text.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                }
            });

            foreach (var text in options)
            {
                if (string.IsNullOrWhiteSpace(text)) continue;

                // Parse centre name and available slots
                // Format: "CENTRE NAME (X slots available)"
                var match = Regex.Match(text, @"^(.+?)\s*\((\d+)\s+slots?\s+available\)$", RegexOptions.IgnoreCase);

                if (match.Success)
                {
                    var centreName = match.Groups[1].Value.Trim();
                    var slotsText = match.Groups[2].Value;

                    if (int.TryParse(slotsText, out var availableSlots))
                    {
                        var centre = new CentreAvailability
                        {
                            Name = centreName,
                            AvailableSlots = availableSlots,
                            IsPreferred = config.PreferredCentres.Contains(centreName),
                            OptionIndex = options.IndexOf(text)
                        };

                        centers.Add(centre);
                        logger.LogDebug("Found centre: {Centre} with {Slots} slots (Preferred: {IsPreferred})",
                            centreName, availableSlots, centre.IsPreferred);
                    }
                }
            }

            // For each center with available slots, extract the dates and times available.
            foreach (var centre in centers.Where(c => c.AvailableSlots > 0))
            {
                await testingCentersDropDown.HighlightAsync();

                await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
                await testingCentersDropDown.SelectOptionAsync(new[] { $"{centre.OptionIndex}: Object" });
                await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

                await Task.Delay(NatisConstants.WaitTimes.Medium);

                var dateTimeOptions = await page.Locator("select[name*='date'] option").AllInnerTextsAsync();
                var dateTimes = new List<DateTime>();
                foreach (var option in dateTimeOptions)
                {
                    if (string.IsNullOrWhiteSpace(option) || option.Contains("Select", StringComparison.OrdinalIgnoreCase))
                        continue;
                    // Attempt to parse date and time
                    if (DateTime.TryParse(option, out var dateTime))
                    {
                        dateTimes.Add(dateTime);
                    }
                    else
                    {
                        logger.LogWarning("Failed to parse date/time option: {Option}", option);
                    }
                }
                centre.DateTimes = dateTimes;
            }

            logger.LogInformation("Found {TotalCentres} centres, {PreferredWithSlots} preferred centres with slots",
                centers.Count, centers.Count(c => c.IsPreferred && c.AvailableSlots > 0));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting available centres");
        }

        return centers;
    }

    private async Task SelectCentreAsync(int centreIndex, string centreName)
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        logger.LogInformation("Selecting centre: {Centre}", centreName);

        await Task.Delay(NatisConstants.WaitTimes.UltraShort);
        var testingCentersDropDown = this.page.Locator("select[name=\"infN\"]");
        await Task.Delay(NatisConstants.WaitTimes.UltraShort);

        await testingCentersDropDown.SelectOptionAsync(new[] { "1: Object" });
        await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

        //await testingCentersDropDown.SelectOptionAsync(new[] { $"{centreIndex + 1}: Object" });

        await Task.Delay(NatisConstants.WaitTimes.UltraShort);
    }

    private async Task SelectEarliestDateAndTimeAsync()
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        logger.LogInformation("Selecting earliest available date and time");

        try
        {
            // Select earliest date
            var dateSelectors = new[]
            {
                NatisConstants.Selectors.DatePicker,
                "input[type='date']",
                "[data-testid*='date']",
                "select[name*='date']"
            };

            foreach (var selector in dateSelectors)
            {
                try
                {
                    var dateElement = await page.QuerySelectorAsync(selector);
                    if (dateElement != null)
                    {
                        // If it's a select dropdown, select the first available option
                        if (await dateElement.GetAttributeAsync("tagName") == "SELECT")
                        {
                            var options = await page.QuerySelectorAllAsync($"{selector} option");
                            if (options.Count > 1) // Skip first option which is usually placeholder
                            {
                                var firstOption = options[1];
                                var value = await firstOption.GetAttributeAsync("value");
                                if (!string.IsNullOrEmpty(value))
                                {
                                    await dateElement.SelectOptionAsync(new[] { value });
                                }
                            }
                        }
                        break;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogDebug(ex, "Failed to select date with selector: {Selector}", selector);
                }
            }

            await Task.Delay(NatisConstants.WaitTimes.Short);

            // Select first available time slot
            var timeSelectors = new[]
            {
                NatisConstants.Selectors.TimeSlotDropdown,
                "select[name*='time']",
                "[data-testid*='time']"
            };

            foreach (var selector in timeSelectors)
            {
                try
                {
                    var timeElement = await page.QuerySelectorAsync(selector);
                    if (timeElement != null)
                    {
                        var options = await page.QuerySelectorAllAsync($"{selector} option");
                        if (options.Count > 1) // Skip first option which is usually placeholder
                        {
                            var firstOption = options[1];
                            var value = await firstOption.GetAttributeAsync("value");
                            if (!string.IsNullOrEmpty(value))
                            {
                                await timeElement.SelectOptionAsync(new[] { value });
                            }
                        }
                        break;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogDebug(ex, "Failed to select time with selector: {Selector}", selector);
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error selecting date and time");
            throw;
        }

        await Task.Delay(NatisConstants.WaitTimes.Medium);
    }

    private async Task CompleteBookingAsync()
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        logger.LogInformation("Completing booking");

        try
        {
            // Click the "BOOK ONLY" button
            var bookButtonSelectors = new[]
            {
                NatisConstants.Selectors.BookOnlyButton,
                "button:has-text('BOOK ONLY')",
                "button:has-text('Book Only')",
                "button:has-text('BOOK')",
                "input[type='submit'][value*='BOOK']"
            };

            foreach (var selector in bookButtonSelectors)
            {
                try
                {
                    var element = await page.QuerySelectorAsync(selector);
                    if (element != null)
                    {
                        await element.ClickAsync();
                        break;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogDebug(ex, "Failed to click book button with selector: {Selector}", selector);
                }
            }

            // Wait for booking confirmation
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            await Task.Delay(NatisConstants.WaitTimes.Long);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error completing booking");
            throw;
        }
    }

    private async Task ClickCancelAsync()
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        logger.LogInformation("Clicking cancel to return to dashboard");

        try
        {
            var cancelSelectors = new[]
            {
                NatisConstants.Selectors.CancelButton,
                "button:has-text('Cancel')",
                "button:has-text('CANCEL')",
                "a:has-text('Cancel')"
            };

            foreach (var selector in cancelSelectors)
            {
                try
                {
                    var element = await page.QuerySelectorAsync(selector);
                    if (element != null)
                    {
                        await element.ClickAsync();
                        break;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogDebug(ex, "Failed to click cancel with selector: {Selector}", selector);
                }
            }

            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            await Task.Delay(NatisConstants.WaitTimes.Medium);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Error clicking cancel button");
        }
    }

    private async Task NavigateToSuccessVideoAsync()
    {
        if (page == null) throw new InvalidOperationException("Browser not initialized");

        try
        {
            logger.LogInformation("Navigating to success video: {Url}", config.SuccessVideoUrl);
            await page.GotoAsync(config.SuccessVideoUrl);
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to navigate to success video");
        }
    }

    private async Task DebugPageElementsAsync()
    {
        if (page == null) return;

        try
        {
            logger.LogInformation("=== PAGE DEBUG INFO ===");

            // Get all links on the page
            var links = await page.QuerySelectorAllAsync("a");
            logger.LogInformation("Found {Count} links on the page", links.Count);

            foreach (var link in links.Take(10)) // Log first 10 links
            {
                var href = await link.GetAttributeAsync("href");
                var text = await link.TextContentAsync();
                var isVisible = await link.IsVisibleAsync();
                logger.LogInformation("Link: href='{Href}', text='{Text}', visible={IsVisible}",
                    href ?? "null", text?.Trim() ?? "null", isVisible);
            }

            // Get all buttons on the page
            var buttons = await page.QuerySelectorAllAsync("button");
            logger.LogInformation("Found {Count} buttons on the page", buttons.Count);

            foreach (var button in buttons.Take(5)) // Log first 5 buttons
            {
                var text = await button.TextContentAsync();
                var isVisible = await button.IsVisibleAsync();
                logger.LogInformation("Button: text='{Text}', visible={IsVisible}",
                    text?.Trim() ?? "null", isVisible);
            }

            // Check for specific text content
            var pageContent = await page.TextContentAsync("body");
            var hasLoginText = pageContent?.Contains("Login", StringComparison.OrdinalIgnoreCase) ?? false;
            var hasSignInText = pageContent?.Contains("Sign In", StringComparison.OrdinalIgnoreCase) ?? false;
            logger.LogInformation("Page contains 'Login': {HasLogin}, 'Sign In': {HasSignIn}", hasLoginText, hasSignInText);

            logger.LogInformation("=== END PAGE DEBUG INFO ===");
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to debug page elements");
        }
    }

    private async Task TakeScreenshotAsync(string filename)
    {
        if (page == null) return;

        try
        {
            var screenshotPath = Path.Combine("screenshots", $"{filename}_{DateTime.Now:yyyyMMdd_HHmmss}.png");
            Directory.CreateDirectory(Path.GetDirectoryName(screenshotPath)!);

            await page.ScreenshotAsync(new PageScreenshotOptions
            {
                Path = screenshotPath,
                FullPage = true
            });

            logger.LogInformation("Screenshot saved: {Path}", screenshotPath);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to take screenshot");
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!disposed && disposing)
        {
            try
            {
                browser?.DisposeAsync().AsTask().Wait(TimeSpan.FromSeconds(5));
                playwright?.Dispose();
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "Error disposing browser resources");
            }

            disposed = true;
        }
    }
}
