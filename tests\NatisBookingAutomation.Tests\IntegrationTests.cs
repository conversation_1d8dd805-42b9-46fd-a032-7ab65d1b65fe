using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Console;
using Microsoft.Playwright;
using Microsoft.Playwright.NUnit;
using NatisBookingAutomation.Core.Interfaces;
using NatisBookingAutomation.Core.Models;
using NatisBookingAutomation.Core.Services;
using NUnit.Framework;

namespace NatisBookingAutomation.Tests;

[TestFixture]
[Category("Integration")]
public class NatisBookingServiceIntegrationTests : PageTest
{
    private ILogger<NatisBookingService> logger = null!;
    private BookingConfiguration config = null!;

    [SetUp]
    public void Setup()
    {
        // Create logger
        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        logger = loggerFactory.CreateLogger<NatisBookingService>();

        // Create test configuration
        config = new BookingConfiguration
        {
            Username = "<EMAIL>", // Use test credentials
            Password = "test-password",
            ProfileName = "TEST USER",
            MaxRetryAttempts = 1, // Limit retries for testing
            RetryDelaySeconds = 1,
            PageTimeoutSeconds = 10,
            HeadlessMode = true // Run headless in tests
        };
    }

    [Test]
    [Ignore("Requires valid NATIS credentials and should not run in CI")]
    public async Task BookMotorcycleLicenseTestAsync_WithValidCredentials_ShouldAttemptBooking()
    {
        // Arrange
        using var service = new NatisBookingService(logger, config);

        // Act
        var result = await service.BookMotorcycleLicenseTestAsync();

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.AttemptsUsed, Is.GreaterThan(0));
        Assert.That(result.Logs, Is.Not.Empty);

        // Note: We don't assert success because it depends on slot availability
        // and we don't want to actually book slots in tests
    }

    [Test]
    public async Task NavigateToNatisPortal_ShouldLoadSuccessfully()
    {
        // Arrange
        await Page.GotoAsync("https://online.natis.gov.za/#/");

        // Act
        await Page.WaitForLoadStateAsync(LoadState.NetworkIdle);

        // Assert
        Assert.That(Page.Url, Does.Contain("natis.gov.za"));

        // Check if page loaded properly by looking for common elements
        var pageContent = await Page.ContentAsync();
        Assert.That(pageContent, Is.Not.Empty);
    }

    [Test]
    public async Task CheckLoginElements_ShouldFindLoginOrProfileElements()
    {
        // Arrange
        await Page.GotoAsync("https://online.natis.gov.za/#/");
        await Page.WaitForLoadStateAsync(LoadState.NetworkIdle);

        // Act & Assert
        // Should find either login elements (if not logged in) or profile elements (if logged in)
        var loginLink = await Page.QuerySelectorAsync("text=Login");
        var registerLink = await Page.QuerySelectorAsync("text=Register");
        var profileElement = await Page.QuerySelectorAsync("[data-testid='profile-name']");

        // At least one of these should be present
        Assert.That(loginLink != null || registerLink != null || profileElement != null, Is.True,
            "Should find either login/register links or profile element");
    }
}

[TestFixture]
[Category("Unit")]
public class NatisBookingServiceUnitTests
{
    private ILogger<NatisBookingService> logger = null!;
    private BookingConfiguration config = null!;

    [SetUp]
    public void Setup()
    {
        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        logger = loggerFactory.CreateLogger<NatisBookingService>();

        config = new BookingConfiguration
        {
            Username = "<EMAIL>",
            Password = "test-password",
            MaxRetryAttempts = 2,
            RetryDelaySeconds = 1,
            PageTimeoutSeconds = 5,
            HeadlessMode = true
        };
    }

    [Test]
    public void NatisBookingService_Constructor_ShouldInitializeCorrectly()
    {
        // Act
        using var service = new NatisBookingService(logger, config);

        // Assert
        Assert.That(service, Is.Not.Null);
        Assert.That(service, Is.InstanceOf<INatisBookingService>());
    }

    [Test]
    public void NatisBookingService_Dispose_ShouldNotThrow()
    {
        // Arrange
        var service = new NatisBookingService(logger, config);

        // Act & Assert
        Assert.DoesNotThrow(() => service.Dispose());
    }

    [Test]
    public async Task IsLoggedInAsync_WithoutBrowser_ShouldReturnFalse()
    {
        // Arrange
        using var service = new NatisBookingService(logger, config);

        // Act
        var result = await service.IsLoggedInAsync();

        // Assert
        Assert.That(result, Is.False);
    }
}

[TestFixture]
[Category("Performance")]
public class PerformanceTests
{
    [Test]
    public void BookingConfiguration_Creation_ShouldBeFast()
    {
        // Arrange
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        for (int i = 0; i < 1000; i++)
        {
            var config = new BookingConfiguration();
        }

        // Assert
        stopwatch.Stop();
        Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(100),
            "Creating 1000 BookingConfiguration instances should take less than 100ms");
    }

    [Test]
    public void BookingResult_Creation_ShouldBeFast()
    {
        // Arrange
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        for (int i = 0; i < 1000; i++)
        {
            var result = new BookingResult();
            result.Logs.Add($"Test log entry {i}");
        }

        // Assert
        stopwatch.Stop();
        Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(100),
            "Creating 1000 BookingResult instances should take less than 100ms");
    }
}
