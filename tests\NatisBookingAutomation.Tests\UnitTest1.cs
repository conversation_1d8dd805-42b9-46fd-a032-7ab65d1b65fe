﻿using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using NatisBookingAutomation.Core.Models;
using NatisBookingAutomation.Core.Services;
using NUnit.Framework;

namespace NatisBookingAutomation.Tests;

[TestFixture]
public class BookingConfigurationTests
{
    [Test]
    public void BookingConfiguration_DefaultValues_ShouldBeValid()
    {
        // Arrange & Act
        var config = new BookingConfiguration();

        // Assert
        Assert.That(config.MaxRetryAttempts, Is.EqualTo(10));
        Assert.That(config.RetryDelaySeconds, Is.EqualTo(30));
        Assert.That(config.PageTimeoutSeconds, Is.EqualTo(30));
        Assert.That(config.HeadlessMode, Is.False);
        Assert.That(config.PreferredCentres, Is.Not.Empty);
        Assert.That(config.PreferredCentres.Count, Is.EqualTo(7));
        Assert.That(config.Province, Is.EqualTo("Gauteng"));
        Assert.That(config.TestCategory, Contains.Substring("MOTOR CYCLE"));
    }

    [Test]
    public void BookingConfiguration_PreferredCentres_ShouldContainExpectedCentres()
    {
        // Arrange
        var config = new BookingConfiguration();
        var expectedCentres = new[]
        {
            "Roodepoort DLTC",
            "Krugersdorp DLTC",
            "Randfontein DLTC",
            "Westonaria DLTC",
            "Langlaagte DLTC",
            "Randburg DLTC",
            "Sandton DLTC"
        };

        // Act & Assert
        foreach (var centre in expectedCentres)
        {
            Assert.That(config.PreferredCentres, Contains.Item(centre));
        }
    }
}

[TestFixture]
public class BookingResultTests
{
    [Test]
    public void BookingResult_DefaultState_ShouldBeFailure()
    {
        // Arrange & Act
        var result = new BookingResult();

        // Assert
        Assert.That(result.Success, Is.False);
        Assert.That(result.Message, Is.Empty);
        Assert.That(result.SelectedCentre, Is.Null);
        Assert.That(result.BookingDate, Is.Null);
        Assert.That(result.BookingTime, Is.Null);
        Assert.That(result.AttemptsUsed, Is.EqualTo(0));
        Assert.That(result.Exception, Is.Null);
        Assert.That(result.Logs, Is.Not.Null);
        Assert.That(result.Logs, Is.Empty);
    }

    [Test]
    public void BookingResult_SuccessfulBooking_ShouldHaveValidData()
    {
        // Arrange
        var result = new BookingResult
        {
            Success = true,
            Message = "Booking completed successfully",
            SelectedCentre = "Roodepoort DLTC",
            BookingDate = DateTime.Today.AddDays(7),
            BookingTime = "09:00",
            AttemptsUsed = 2
        };

        // Act & Assert
        Assert.That(result.Success, Is.True);
        Assert.That(result.Message, Is.EqualTo("Booking completed successfully"));
        Assert.That(result.SelectedCentre, Is.EqualTo("Roodepoort DLTC"));
        Assert.That(result.BookingDate, Is.Not.Null);
        Assert.That(result.BookingTime, Is.EqualTo("09:00"));
        Assert.That(result.AttemptsUsed, Is.EqualTo(2));
    }
}

[TestFixture]
public class CentreAvailabilityTests
{
    [Test]
    public void CentreAvailability_PreferredCentre_ShouldBeMarkedCorrectly()
    {
        // Arrange
        var config = new BookingConfiguration();
        var centre = new CentreAvailability
        {
            Name = "Roodepoort DLTC",
            AvailableSlots = 5,
            IsPreferred = config.PreferredCentres.Contains("Roodepoort DLTC")
        };

        // Act & Assert
        Assert.That(centre.Name, Is.EqualTo("Roodepoort DLTC"));
        Assert.That(centre.AvailableSlots, Is.EqualTo(5));
        Assert.That(centre.IsPreferred, Is.True);
    }

    [Test]
    public void CentreAvailability_NonPreferredCentre_ShouldNotBeMarked()
    {
        // Arrange
        var config = new BookingConfiguration();
        var centre = new CentreAvailability
        {
            Name = "Unknown DLTC",
            AvailableSlots = 3,
            IsPreferred = config.PreferredCentres.Contains("Unknown DLTC")
        };

        // Act & Assert
        Assert.That(centre.Name, Is.EqualTo("Unknown DLTC"));
        Assert.That(centre.AvailableSlots, Is.EqualTo(3));
        Assert.That(centre.IsPreferred, Is.False);
    }
}
