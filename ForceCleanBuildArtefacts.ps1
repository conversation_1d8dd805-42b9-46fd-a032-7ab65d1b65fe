function ForceClean-Directory ([string]$directoryPath)
{
    $exists = Test-Path $directoryPath
    if ($exists)
    {
        Write-Host "Directory found at" $directoryPath
        Write-Host "Deleting" $directoryPath
        Remove-Item $directoryPath -Recurse -Force
        Write-Host "Deleted" $directoryPath
    }
    else
    {
        Write-Host "No directory found at" $directoryPath
    }
}

$projects = Get-ChildItem *.csproj -recurse
foreach ($project in $projects)
{
    $projectDirectory = $project.Directory
    Write-Host "Cleaning project" $projectDirectory.Name
    $objDirectory = Join-Path $projectDirectory.FullName "obj"
    ForceClean-Directory $objDirectory
    $binDirectory = Join-Path $projectDirectory.FullName "bin"
    ForceClean-Directory $binDirectory
}