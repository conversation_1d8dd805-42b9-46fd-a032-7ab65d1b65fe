using NatisBookingAutomation.Core.Models;

namespace NatisBookingAutomation.Core.Interfaces;

public interface INatisBookingService
{
    Task<BookingResult> BookMotorcycleLicenseTestAsync(CancellationToken cancellationToken = default);
    Task<bool> IsLoggedInAsync(CancellationToken cancellationToken = default);
    Task<bool> LoginAsync(CancellationToken cancellationToken = default);
    Task<List<CentreAvailability>> GetAvailableCentersAsync(CancellationToken cancellationToken = default);
}
